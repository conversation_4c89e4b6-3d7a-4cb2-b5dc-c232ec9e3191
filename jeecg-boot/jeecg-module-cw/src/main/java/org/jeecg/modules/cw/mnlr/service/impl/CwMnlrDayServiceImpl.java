package org.jeecg.modules.cw.mnlr.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.enums.CwBaseKeyName;
import org.jeecg.modules.cw.base.service.ICwKBaseService;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.frdw.service.ICwFrdwService;
import org.jeecg.modules.cw.jscb.entity.CwJsZcb;
import org.jeecg.modules.cw.jscb.result.CwJscbQueryResult;
import org.jeecg.modules.cw.jscb.service.ICwJscbService;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDay;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDayRow;
import org.jeecg.modules.cw.mnlr.mapper.CwMnlrDayMapper;
import org.jeecg.modules.cw.mnlr.param.CwMnlrDaySumbitParam;
import org.jeecg.modules.cw.mnlr.result.CwMnlrDayQueryResult;
import org.jeecg.modules.cw.mnlr.result.CwMnlrStatisticsResult;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrDayService;
import org.jeecg.modules.cw.qtfy.service.ICwQtfyService;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Lazy;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import javax.swing.text.html.Option;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import cn.hutool.core.date.DateUtil;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import org.jeecg.modules.cw.quartz.service.IPriceDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Description: 矿模拟利润（日）
 * @Author: jeecg-boot
 * @Date: 2025-01-09
 * @Version: V1.0
 */
@Service
public class CwMnlrDayServiceImpl extends ServiceImpl<CwMnlrDayMapper, CwMnlrDay> implements ICwMnlrDayService {

    private static final String DICT_TYPE = "mnlrDay";
    private static final Logger log = LoggerFactory.getLogger(CwMnlrDayServiceImpl.class);

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwQtfyService qtfyService;
    @Resource
    private ICwFrdwService frdwService;
    @Resource
    private ICwKBaseService kBaseService;
    @Resource
    private ICwJscbService jscbService;
    @Lazy
    @Resource
    private IPriceDataService priceDataService;

    private final ExecutorService autoFillExecutor = Executors.newSingleThreadExecutor();

    @Override
    public CwMnlrDayQueryResult queryByDate(Date queryDate) {
        CwMnlrDayQueryResult result = new CwMnlrDayQueryResult();
        result.setQueryDate(queryDate);
        // 基础数据
        Optional.ofNullable(qtfyService.sumDay(queryDate)).ifPresent(v -> result.setQtfy(v.toString()));
        Optional.ofNullable(frdwService.sumDay(queryDate)).ifPresent(v -> result.setFrdw(v.toString()));

        String jh = kBaseService.getCwBaseDataYear(CwBaseKeyName.Year_JH, queryDate);
        if (ObjectUtil.isNotEmpty(jh)) {
            result.setJh(jh);
        }
        // 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);
        // 已有列表
        List<CwMnlrDay> cb = this.lambdaQuery()
                .eq(CwMnlrDay::getRecordTime, queryDate)
                .list();
        // 矿总成本
        BigDecimal zcb = kBaseService.getTotalDrs(queryDate);
        result.setZcb(zcb);
                
        // 获取当日成本信息
        Map<String, BigDecimal> dayCbMap = getDayCbMap(queryDate);
        
        // 合并数据
        List<CwMnlrDayRow> resRows = new ArrayList<>();
        for (CwNameDict d : dict) {
            CwMnlrDayRow row = new CwMnlrDayRow();
            BeanUtil.copyProperties(d, row);
            // 数据
            cb.stream().filter((v) -> v.getName().equals(d.getName()))
                    .findFirst()
                    .ifPresent(v -> BeanUtil.copyProperties(v, row));
                    
            // 设置当日成本
            String type = d.getType();
            if (dayCbMap.containsKey(type)) {
                BigDecimal dayCb = dayCbMap.get(type);
                if (dayCb != null) {
                    row.setCb(dayCb.toString());
                }
            }
            
            BigDecimal xs = CwMnlrDay.getXs(row.getType());
            row.setXs(xs == null ? null : xs.toString());
            // 添加
            resRows.add(row);
        }
        result.setRows(resRows);
        // 结果
        return result;
    }
    
    /**
     * 获取当日成本Map
     * @param queryDate 查询日期
     * @return 当日成本Map，key为type，value为当日成本
     */
    private Map<String, BigDecimal> getDayCbMap(Date queryDate) {
        Map<String, BigDecimal> dayCbMap = new HashMap<>();
        
        // 当天成本
        List<CwJsZcb> currentDayCb = jscbService.getJscb(queryDate);
        
        // 检查是否是本月第一天
        boolean isFirstDayOfMonth = DateUtil.beginOfMonth(queryDate).equals(DateUtil.beginOfDay(queryDate));
        
        if (isFirstDayOfMonth) {
            // 如果是本月第一天，直接使用当天的成本数据
            if (currentDayCb != null) {
                for (CwJsZcb item : currentDayCb) {
                    if (item.getZcb() != null) {
                        dayCbMap.put(item.getType(), item.getZcb());
                    }
                }
            }
            return dayCbMap;
        }
        
        // 前一天成本
        Date previousDay = DateUtil.offsetDay(queryDate, -1);
        List<CwJsZcb> previousDayCb = jscbService.getJscb(previousDay);
        
        // 计算当日成本差值
        if (currentDayCb != null && previousDayCb != null) {
            Map<String, BigDecimal> previousMap = new HashMap<>();
            
            // 将前一天的成本放入map中
            for (CwJsZcb item : previousDayCb) {
                if (item.getZcb() != null) {
                    previousMap.put(item.getType(), item.getZcb());
                }
            }
            
            // 计算当日成本（当天累计成本 - 前一天累计成本）
            for (CwJsZcb item : currentDayCb) {
                if (item.getZcb() != null) {
                    BigDecimal previousCb = previousMap.getOrDefault(item.getType(), BigDecimal.ZERO);
                    BigDecimal dayCb = item.getZcb().subtract(previousCb);
                    dayCbMap.put(item.getType(), dayCb);
                }
            }
        } else if (currentDayCb != null) {
            // 如果没有前一天的数据，则当天累计成本即为当日成本
            for (CwJsZcb item : currentDayCb) {
                if (item.getZcb() != null) {
                    dayCbMap.put(item.getType(), item.getZcb());
                }
            }
        }
        
        return dayCbMap;
    }

    @Override
    public void submit(CwMnlrDaySumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 更新行数据
        List<CwMnlrDayRow> rows = param.getRows();
        this.remove(new LambdaQueryWrapper<>(CwMnlrDay.class)
                .eq(CwMnlrDay::getRecordTime, submitDate));
        ArrayList<CwMnlrDay> days = new ArrayList<>();
        for (CwMnlrDayRow row : rows) {
            CwMnlrDay day = new CwMnlrDay();
            BeanUtil.copyProperties(row, day);
            day.setRecordTime(submitDate);
            days.add(day);
        }
        this.saveBatch(days);
    }

    // 计算模拟利润
    @Override
    public BigDecimal calculateMnlr(Date queryDate) {
        // 获取收入总和（收入=销售收入总和）
        BigDecimal slSum = calculateSl(queryDate);
        
        // 获取成本总和
        BigDecimal cbSum = calculateCb(queryDate);
        
        // 获取其他费用
        BigDecimal qtfy = getQt(queryDate);
        
        // 获取分摊的第三方利润（分入/分出）
        BigDecimal frdw = getDjlr(queryDate);
        
        // 计算模拟利润 = 收入总和 - 成本总和 - 其他费用 + 分入的第三方利润
        BigDecimal mnlr = slSum.subtract(cbSum).subtract(qtfy).add(frdw);
        
        return mnlr;
    }
    
    /**
     * 计算计划比（模拟利润与计划的比较差额）
     * @param queryDate 查询日期
     * @return 计划比
     */
    @Override
    public BigDecimal calculateJhb(Date queryDate) {
        // 计算模拟利润
        BigDecimal mnlr = calculateMnlr(queryDate);
        
        // 计算日计划（月计划除以当月天数）
        BigDecimal jhDay = calculateGsjh(queryDate);
        
        // 计算计划比（实际模拟利润与计划的差额）
        return mnlr.subtract(jhDay);
    }
    
    @Override
    public BigDecimal calculateSl(Date queryDate) {
        // 直接从数据库获取当天的模拟利润行数据，按公式计算销售收入
        List<CwMnlrDay> dayRows = this.lambdaQuery()
                .between(CwMnlrDay::getRecordTime, DateUtil.beginOfDay(queryDate), DateUtil.endOfDay(queryDate))
                .list();

        if (dayRows == null || dayRows.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal slSum = BigDecimal.ZERO;
        for (CwMnlrDay day : dayRows) {
            BigDecimal jg = day.getJg();
            BigDecimal xl = day.getXl();
            if (jg == null || xl == null) {
                continue; // 跳过数据不完整的记录
            }
            BigDecimal xs = CwMnlrDay.getXs(day.getType());
            BigDecimal singleSale = jg.multiply(xs).multiply(xl)
                    .divide(new BigDecimal("1.13"), 10, RoundingMode.HALF_UP)
                    .divide(new BigDecimal("10000"), 10, RoundingMode.HALF_UP);
            slSum = slSum.add(singleSale);
        }

        return slSum;
    }
    
    @Override
    public BigDecimal calculateCb(Date queryDate) {
        return kBaseService.getTotalDrs(queryDate);
    }
    
    @Override
    public BigDecimal getDjlr(Date queryDate) {
        BigDecimal frdw = frdwService.sumDay(queryDate);
        return frdw == null ? BigDecimal.ZERO : frdw;
    }

    @Override
    public BigDecimal getQt(Date queryDate) {
        BigDecimal qtfy = qtfyService.sumDay(queryDate);
        return qtfy == null ? BigDecimal.ZERO : qtfy;
    }

    @Override
    public BigDecimal calculateGsjh(Date queryDate) {
        String jhStr = kBaseService.getCwBaseDataYear(CwBaseKeyName.Year_JH, queryDate);
        if (ObjectUtil.isEmpty(jhStr)) {
            return BigDecimal.ZERO;
        }
        BigDecimal yearPlan = new BigDecimal(jhStr);
        // 将年度计划平均拆分到每月（12）再到每日。此处保持原有“/12”的业务逻辑，如需按天数拆分请在此调整。
        return yearPlan.divide(new BigDecimal("12"), 2, RoundingMode.HALF_UP);
    }
    
    @Override
    public Map<String, BigDecimal> calculateAllStatistics(Date queryDate) {
        Map<String, BigDecimal> resultMap = new HashMap<>();
        
        BigDecimal sl = calculateSl(queryDate);
        BigDecimal cb = calculateCb(queryDate);
        BigDecimal djlr = getDjlr(queryDate);
        BigDecimal qt = getQt(queryDate);
        BigDecimal gsjh = calculateGsjh(queryDate);
        BigDecimal mnlr = sl.subtract(cb).subtract(qt).add(djlr);
        BigDecimal jhb = mnlr.subtract(gsjh);
        
        resultMap.put("sl", sl);
        resultMap.put("cb", cb);
        resultMap.put("djlr", djlr);
        resultMap.put("qt", qt);
        resultMap.put("mnlr", mnlr);
        resultMap.put("gsjh", gsjh);
        resultMap.put("jhb", jhb);
        
        return resultMap;
    }
    
    @Override
    public CwMnlrStatisticsResult calculateStatistics(Date queryDate) {
        CwMnlrStatisticsResult result = new CwMnlrStatisticsResult();
        result.setRecordTime(queryDate);
        
        Map<String, BigDecimal> stats = calculateAllStatistics(queryDate);
        
        result.setSl(stats.get("sl"));
        result.setCb(stats.get("cb"));
        result.setDjlr(stats.get("djlr"));
        result.setQt(stats.get("qt"));
        result.setMnlr(stats.get("mnlr"));
        result.setGsjh(stats.get("gsjh"));
        result.setJhb(stats.get("jhb"));
        
        return result;
    }

    @Override
    public CwMnlrDayQueryResult autoFill(Date queryDate) {
        // 同步拉取当天数据（queryDate）
        priceDataService.getPriceData(queryDate);
        priceDataService.getOutputDataAndUpdate(queryDate);

        // 异步补齐
        CompletableFuture.runAsync(() -> {
            Date today = DateUtil.beginOfDay(new Date());
            Date monthStart = DateUtil.beginOfMonth(queryDate); // 查询月份第一天
            // 判断查询月份是否为现实时间当前月份
            Date realMonthStart = DateUtil.beginOfMonth(today);
            Date monthEnd;
            if (monthStart.equals(realMonthStart)) {
                // 当前月：补齐到现实昨天
                monthEnd = DateUtil.offsetDay(today, -1);
            } else {
                // 非当前月：补齐整月
                monthEnd = DateUtil.endOfMonth(queryDate);
            }

            for (Date date = monthStart; !date.after(monthEnd); date = DateUtil.offsetDay(date, 1)) {
                // queryDate 已同步处理，跳过
                if (DateUtil.isSameDay(date, queryDate)) {
                    continue;
                }
                try {
                    List<CwMnlrDay> dayRows = this.lambdaQuery()
                            .between(CwMnlrDay::getRecordTime, DateUtil.beginOfDay(date), DateUtil.endOfDay(date))
                            .list();
                    boolean needFill = dayRows == null || dayRows.isEmpty() || dayRows.stream().anyMatch(d -> d.getJg() == null || d.getXl() == null);
                    if (needFill) {
                        priceDataService.getPriceData(date);
                        priceDataService.getOutputDataAndUpdate(date);
                    }
                } catch (Exception e) {
                    log.error("异步自动填充{}的数据失败", DateUtil.formatDate(date), e);
                }
            }
        }, autoFillExecutor);

        // 返回当天最新数据
        return this.queryByDate(queryDate);
    }
}
