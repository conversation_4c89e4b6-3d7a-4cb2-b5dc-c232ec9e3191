package org.jeecg.modules.cw.jlfx.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.cw.base.entity.CwNameDict;
import org.jeecg.modules.cw.base.service.ICwNameDictService;
import org.jeecg.modules.cw.ckc.service.ICwCkcZhcbService;
import org.jeecg.modules.cw.ds.service.ICwDsZhcbService;
import org.jeecg.modules.cw.jlfx.entity.CwJlfxMonth;
import org.jeecg.modules.cw.jlfx.entity.CwJlfxRow;
import org.jeecg.modules.cw.jlfx.mapper.CwJlfxMonthMapper;
import org.jeecg.modules.cw.jlfx.param.CwJlfxSumbitParam;
import org.jeecg.modules.cw.jlfx.result.CwJlfxQueryResult;
import org.jeecg.modules.cw.jlfx.service.ICwJlfxMonthService;
import org.jeecg.modules.cw.jw.service.ICwJwZhcbService;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDay;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrMonthRow;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrDayService;
import org.jeecg.modules.cw.sx.service.ICwSxZhcbService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @Description: 价量分析表
 * @Author: jeecg-boot
 * @Date: 2025-01-03
 * @Version: V1.0
 */
@Service
public class CwJlfxMonthServiceImpl extends ServiceImpl<CwJlfxMonthMapper, CwJlfxMonth> implements ICwJlfxMonthService {

    private static final String DICT_TYPE = "jlfx";

    @Resource
    private ICwNameDictService nameDictService;
    @Resource
    private ICwMnlrDayService mnlrDayService;

    @Override
    public CwJlfxQueryResult queryByDate(Date queryDate) {
        CwJlfxQueryResult result = new CwJlfxQueryResult();
        result.setQueryDate(queryDate);
        // 项目字典
        List<CwNameDict> dict = nameDictService.queryList(DICT_TYPE);

        // 获取当月所有日模拟利润数据
        List<CwMnlrDay> dayDataList = mnlrDayService.lambdaQuery()
                .ge(CwMnlrDay::getRecordTime, DateUtil.beginOfMonth(queryDate))
                .le(CwMnlrDay::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();
                
        // 计算当月累计销量和加权平均价格
        Map<String, BigDecimal> typeToTotalXl = new HashMap<>();
        Map<String, BigDecimal> typeToTotalAmount = new HashMap<>();
        Map<String, BigDecimal> typeToValidXl = new HashMap<>();
        
        // 计算当月累计销量和总金额
        for (CwMnlrDay day : dayDataList) {
            String type = day.getType();
            BigDecimal xl = day.getXl() != null ? day.getXl() : BigDecimal.ZERO;
            BigDecimal jg = day.getJg() != null ? day.getJg() : BigDecimal.ZERO;
            
            // 累计销量
            typeToTotalXl.merge(type, xl, BigDecimal::add);
            
            // 累计金额(价格×销量)，只有当价格和销量都不为0时才计算
            if (xl.compareTo(BigDecimal.ZERO) > 0 && jg.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal amount = jg.multiply(xl);
                typeToTotalAmount.merge(type, amount, BigDecimal::add);
                
                // 记录有效销量（用于计算加权平均价格）
                typeToValidXl.merge(type, xl, BigDecimal::add);
            }
        }
        
        // 已有列表
        List<CwJlfxMonth> jlfx = this.lambdaQuery()
                .ge(CwJlfxMonth::getRecordTime, DateUtil.beginOfMonth(queryDate))
                .le(CwJlfxMonth::getRecordTime, DateUtil.endOfMonth(queryDate))
                .list();
        // 合并数据
        List<CwJlfxRow> resRows = new ArrayList<>();
        for (CwNameDict d : dict) {
            CwJlfxRow row = new CwJlfxRow();
            // 数据
            BeanUtil.copyProperties(d, row);
            jlfx.stream().filter((v) -> d.getName().equals(v.getName()))
                    .findFirst()
                    .ifPresent(v -> {
                        BeanUtil.copyProperties(v, row);
                    });

            String type = d.getType();
            String nameKey = d.getNameKey();

            // 处理钼产品的映射关系：价量分析中nameKey是'm'，但模拟利润数据中type是'mjk'
            String mappedNameKey = nameKey;
            if ("m".equals(nameKey)) {
                mappedNameKey = "mjk";
            }

            // 设置累计销量
            if (type.equals("xl")) {
                BigDecimal totalXl = typeToTotalXl.getOrDefault(mappedNameKey, null);
                if (totalXl != null) {
                    row.setSj(totalXl);
                }
            }
            // 设置加权平均价格
            else if (type.equals("sj")) {
                BigDecimal validXl = typeToValidXl.getOrDefault(mappedNameKey, BigDecimal.ZERO);
                if (validXl.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal totalAmount = typeToTotalAmount.getOrDefault(mappedNameKey, null);
                    if (totalAmount != null) {
                        BigDecimal avgPrice = totalAmount.divide(validXl, 3, RoundingMode.HALF_UP);
                        row.setSj(avgPrice);
                    }
                }
            }
            // 添加
            resRows.add(row);
        }
        result.setRows(resRows);
        // 结果
        return result;
    }


    @Override
    public CwJlfxQueryResult autoFill(Date queryDate) {
        // 从上个月开始，逐月回溯查找数据
        for (int i = 1; i < 12; i++) { // 最多回溯11次，即一年内
            Date dateToTry = DateUtil.offset(queryDate, DateField.MONTH, -i);
            long count = this.lambdaQuery()
                    .between(CwJlfxMonth::getRecordTime, DateUtil.beginOfMonth(dateToTry), DateUtil.endOfMonth(dateToTry))
                    .count();
            if (count > 0) {
                // 找到数据，立即使用这个日期进行查询并返回
                return queryByDate(dateToTry);
            }
        }
        // 如果一年内都找不到数据，则默认查询当天
        return queryByDate(queryDate);
    }

    @Override
    public void submit(CwJlfxSumbitParam param) {
        Date submitDate = param.getSubmitDate();
        // 更新行数据
        List<CwJlfxRow> rows = param.getRows();
        this.remove(new LambdaQueryWrapper<>(CwJlfxMonth.class)
                .ge(CwJlfxMonth::getRecordTime, DateUtil.beginOfMonth(submitDate))
                .le(CwJlfxMonth::getRecordTime, DateUtil.endOfMonth(submitDate)));
        ArrayList<CwJlfxMonth> months = new ArrayList<>();
        for (CwJlfxRow row : rows) {
            CwJlfxMonth month = new CwJlfxMonth();
            BeanUtil.copyProperties(row, month);
            month.setRecordTime(submitDate);
            months.add(month);
        }
        this.saveBatch(months);
    }
}
