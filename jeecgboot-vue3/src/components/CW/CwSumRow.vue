<template>
  <!-- 合计行 -->
  <div class="row subtotal">
    <div>合计</div>
    <div>万元</div>
    <div class="span-3">小计</div>
    <!--  单位成本  -->
    <div>{{ formatNumber(hj.dwcb) }}</div>
    <!--  当日数  -->
    <div>{{ formatNumber(hj.drs) }}</div>
    <!--  月累计  -->
    <div>{{ formatNumber(hj.ylj) }}</div>
    <!-- 月预算   -->
    <div>{{ formatNumber(hj.yys) }}</div>
    <!-- 月预测   -->
    <div>{{ formatNumber(hj.yyc) }}</div>
    <!--  进度  -->
    <div>{{ formatNumber(hj.jd) }}%</div>
    <!--  备注  -->
    <div></div>
  </div>
</template>
<script setup lang="ts">
  import { computed } from 'vue';
  import { formatNumber } from '@/utils/showUtils';

  const props = defineProps({
    xjRows: {
      type: Array<any>,
      default: () => [],
    },
  });
  const hj = computed(() => {
    console.log(props.xjRows);
    let drs = props.xjRows.reduce((acc, cur) => acc + cur.drs || 0, 0);
    let yys = props.xjRows.reduce((acc, cur) => acc + cur.yys || 0, 0);
    let ylj = props.xjRows.reduce((acc, cur) => acc + cur.ylj || 0, 0) + drs || 0;
    let yyc = props.xjRows.reduce((acc, cur) => acc + cur.yyc || 0, 0);
    return {
      dwcb: props.xjRows.reduce((acc, cur) => acc + cur.dwcb || 0, 0),
      drs: drs,
      ylj: ylj,
      yys: yys,
      yyc: yyc,
      jd: (100 * ylj) / yys,
    };
  });
</script>
<style lang="less" scoped>
  /* 容器样式 */
  .table-container {
    width: 100%;
    //max-width: 1800px;
    margin: 0 auto;
    background: linear-gradient(180deg, #f5f7fa 0%, #eef1f5 100%);
    padding: 20px;
    border-radius: 12px;
    box-shadow:
      0 4px 6px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08);
  }

  /* 表格基础样式 */
  .grid-table {
    display: grid;
    gap: 5px;
    border-radius: 6px;
    overflow: hidden;
    background-color: #ffffff; /* 确保表格内部有清晰对比 */
  }

  /* 行样式 */
  .row {
    display: contents; /* 让子元素直接成为网格项 */
  }

  .custom-center-text :deep(.ant-input-number-input) {
    text-align: center !important;
  }
  /* 表头样式 */
  .header {
    font-weight: bold;
    color: white;
    background: linear-gradient(135deg, #007bff, #1e90ff);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  /* 定义每一列的宽度 */
  .grid-table > div {
    display: grid;
    grid-template-columns: repeat(12, 1fr); /* 12 列 */
    padding: 10px;
    text-align: center;
    border-right: 1px solid #eaeaea;
    transition: background-color 0.2s;

    &:last-child {
      border-right: none;
    }
  }

  /* 单元格悬停效果 */
  .grid-table > div:hover {
    background-color: #e9ecef;
  }

  /* 小计单元格样式 */
  .subtotal .span-3 {
    grid-column: span 3; /* 跨越3列 */
    background-color: #e6ffed;
    color: #28a745;
    font-weight: bold;
  }

  /* 小计行项目名称样式 */
  .subtotal > div:first-child {
    font-weight: bold;
  }

  /* 行交替背景色 */
  .grid-table > :nth-child(even) {
    background-color: #f8f9fa;
  }

  .grid-table > :nth-child(odd) {
    background-color: #ffffff;
  }
</style>
