<template>
  <div class="table-container">
    <div class="toolbar">
      <div class="toolbar-actions">
        <a-button type="primary" @click="submit">提交</a-button>
      </div>
      <div class="toolbar-filters">
        <span>日期范围:</span>
        <a-date-picker v-model:value="dateRange" :presets="presets" @change="dateRangeChange" :allowClear="false" />
      </div>
    </div>
    <div class="flex justify-between items-center">
      <div></div>
      <div class="title items-center">
        <div>采矿场综合成本表</div>
        <div v-show="!isSave" class="text-red">*</div>
      </div>
      <div class="operator">
        <a-tooltip>
          <template #title>自动填充</template>
          <SisternodeOutlined class="mr-6 text-lg" @click="clickAutoFill" />
        </a-tooltip>
        <div class="mr-10"></div>
      </div>
    </div>

    <div class="grid-table">
      <!-- 表头 -->
      <div class="row header">
        <div>项目</div>
        <div>单位</div>
        <div>平均单耗</div>
        <div>平均总耗</div>
        <div>平均单价</div>
        <div>单位成本</div>
        <div>当日数</div>
        <div>月累计</div>
        <div>月预算</div>
        <div class="flex items-center justify-center">
          月预测
          <a-tag class="ml-1" :color="canEditYyc ? 'green' : 'orange'" size="small">
            {{ getYycStatusText() }}
          </a-tag>
        </div>
        <div>进度（%）</div>
      </div>
      <!-- 数据行 -->
      <div class="row">
        <div>采剥总量</div>
        <div>万吨</div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="ckcData.cbzl" /></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
      <div class="row">
        <div>月处理量预测</div>
        <div>万吨</div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="ckcData.cllyc" /></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
      <cw-row v-if="show" :rows="clList" :base="{ xj: ckcData.cbzl, xq: ckcData.cbzl, cllyc: ckcData.cllyc, yyc: ckcData.yyc }" type-name="材料" @change="rowChange" :canEditYyc="canEditYyc" :canEditYys="canEditYys" :isFirstHalf="isFirstHalf" />
      <cw-row v-if="show" :rows="bjList" :base="{ xj: ckcData.cbzl, xq: ckcData.cbzl, cllyc: ckcData.cllyc, yyc: ckcData.yyc }" type-name="备件" @change="rowChange" :canEditYyc="canEditYyc" :canEditYys="canEditYys" :isFirstHalf="isFirstHalf" />
      <cw-row v-if="show" :rows="rlList" :base="{ xj: ckcData.cbzl, xq: ckcData.cbzl, cllyc: ckcData.cllyc, yyc: ckcData.yyc }" type-name="燃料" @change="rowChange" :canEditYyc="canEditYyc" :canEditYys="canEditYys" :isFirstHalf="isFirstHalf" />
      <cw-row v-if="show" :rows="qtList" :base="{ xj: ckcData.cbzl, xq: ckcData.cbzl, cllyc: ckcData.cllyc, yyc: ckcData.yyc }" type-name="其他" @change="rowChange" :canEditYyc="canEditYyc" :canEditYys="canEditYys" :isFirstHalf="isFirstHalf" />
      <div v-show="false">
        <cw-row :rows="dlList" :base="{ xj: ckcData.cbzl, xq: ckcData.cbzl, cllyc: ckcData.cllyc, yyc: ckcData.yyc }" type-name="动力" @change="rowChange" :canEditYyc="canEditYyc" :canEditYys="canEditYys" :isFirstHalf="isFirstHalf" />
        <cw-row :rows="zzfyList" :base="{ xj: ckcData.cbzl, xq: ckcData.cbzl, cllyc: ckcData.cllyc, yyc: ckcData.yyc }" type-name="制造费用" @change="rowChange" :canEditYyc="canEditYyc" :canEditYys="canEditYys" :isFirstHalf="isFirstHalf" />
      </div>
      <cw-sum-row :xjRows="xjRows" />
    </div>
  </div>
</template>
<script lang="ts" setup name="cw-ckc-zhcb">
  import { computed, onMounted, ref, watch } from 'vue';
  // 项目字典
  import { defHttp } from '@/utils/http/axios';
  import dayjs, { Dayjs } from 'dayjs';
  import CwRow from '@/components/CW/CwRow.vue';
  import CwSumRow from '@/components/CW/CwSumRow.vue';
  import { SisternodeOutlined, QuestionCircleOutlined, InfoCircleOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';

  // 获取数据
  const clList: any = ref([]);
  const bjList: any = ref([]);
  const rlList: any = ref([]);
  const qtList: any = ref([]);
  const clxjList: any = ref([]);
  const bjxjList: any = ref([]);
  const rlxjList: any = ref([]);
  const qtxjList: any = ref([]);
  const ckcData: any = ref({
    cbzl: 0, // 采剥总量（当日数）
    cllyc: 0, // 处理量预测
    yyc: 0, // 月预测
  });
  const dlList: any = ref([]);
  const zzfyList: any = ref([]);
  const dlxjList: any = ref([]);
  const zzfyxjList: any = ref([]);
  const show = ref(false);
  const isSave = ref(true);
  // 月预测填写状态
  const canEditYyc = ref(true); // 月预测可编辑
  const canEditYys = ref(true); // 月预算可编辑
  const yycFirstHalfFilled = ref(false);
  const yycSecondHalfFilled = ref(false);
  
  // 日期选择
  const dateRange = ref(dayjs(new Date()));
  const isFirstHalf = computed(() => dateRange.value.date() <= 15);
  onMounted(async () => {
    await httpGetData(dateRange.value);
  });
  // 合计数据
  const xjRows = computed(() => {
    return [clxjList.value, bjxjList.value, rlxjList.value, qtxjList.value];
  });
  // 是否保存
  let firstWatch = true;
  watch(
    () => xjRows,
    () => {
      if (firstWatch) {
        firstWatch = false;
        return;
      }
      isSave.value = false;
    },
    { deep: true }
  );
  // 日期选择器变化
  const dateRangeChange = async (v) => {
    await httpGetData(dayjs(v));
  };
  const rowChange = (rows, xj) => {
    switch (rows.tag) {
      case 'cl':
        clList.value = rows;
        clxjList.value = xj.value;
        break;
      case 'bj':
        bjList.value = rows;
        bjxjList.value = xj.value;
        break;
      case 'rl':
        rlList.value = rows;
        rlxjList.value = xj.value;
        break;
      case 'qt':
        qtList.value = rows;
        qtxjList.value = xj.value;
        break;
      case 'dl':
        dlList.value = rows;
        dlxjList.value = xj.value;
        break;
      case 'zzfy':
        zzfyList.value = rows;
        zzfyxjList.value = xj.value;
        break;
    }
  };
  
  // 获取月预测填写状态文本
  const getYycStatusText = () => {
    const currentDay = dateRange.value.date();
    const daysInMonth = dateRange.value.daysInMonth();
    if (currentDay <= 15) {
      return "上半月";
    }
    if (currentDay === daysInMonth) {
      return "月末";
    }
    return "下半月";
  };
  
  // 提交
  const submit = async () => {
    // 提交数据
    let recordTime = dayjs(dateRange.value).format('YYYY-MM-DD');
    const currentDay = dateRange.value.date();
    const isFirstHalf = currentDay <= 15;
    
    try {
      await defHttp.post({
        url: 'ckc/cwCkcZhcb/submit',
        params: {
          submitDate: recordTime,
          base: ckcData.value,
          rows: [...clList.value, ...bjList.value, ...rlList.value, ...qtList.value],
          isFirstHalf: isFirstHalf
        },
      });
      isSave.value = true;
    } catch (error) {
      console.error('提交失败', error);
    }
  };

  const clickAutoFill = async () => {
    const res = await defHttp.get({
      url: 'ckc/cwCkcZhcb/autoFill',
      params: {
        queryDate: dayjs(dateRange.value).format('YYYY-MM-DD'),
      },
    });
    if (res == null) {
      message.warn('无数据填充');
      return;
    }
    ckcData.value = {
      cbzl: res.cbzl,
      cllyc: res.yc || 0,
      yyc: res.yyc || 0,
    };
    clList.value.forEach((item) => {
      let find = res.rows.find((row) => row.name === item.name);
      item.pjzh = find?.pjzh;
      item.pjdj = find?.pjdj;
      item.yys = find?.yys;
      item.yyc = find?.yyc || 0;
      // 单位成本和当日数通过计算获得，不需要赋值
    });
    bjList.value.forEach((item) => {
      let find = res.rows.find((row) => row.name === item.name);
      item.pjzh = find?.pjzh;
      item.pjdj = find?.pjdj;
      item.yys = find?.yys;
      item.yyc = find?.yyc || 0;
      // 单位成本和当日数通过计算获得，不需要赋值
    });
    rlList.value.forEach((item) => {
      let find = res.rows.find((row) => row.name === item.name);
      item.pjzh = find?.pjzh;
      item.pjdj = find?.pjdj;
      item.yys = find?.yys;
      item.yyc = find?.yyc || 0;
      // 单位成本和当日数通过计算获得，不需要赋值
    });
    qtList.value.forEach((item) => {
      let find = res.rows.find((row) => row.name === item.name);
      item.pjzh = find?.pjzh;
      item.pjdj = find?.pjdj;
      item.yys = find?.yys;
      item.yyc = find?.yyc || 0;
      // 单位成本和当日数通过计算获得，不需要赋值
    });

    message.info('填充完成');
  };

  // =====================================公共函数
  const httpGetData = async (date: Dayjs) => {
    show.value = false;
    let queryDate = dayjs(date).format('YYYY-MM-DD');
    const res = await defHttp.get({ url: '/ckc/cwCkcZhcb/query', params: { queryDate } });
    ckcData.value = {
      cbzl: res.cbzl,
      cllyc: res.yc || 0,
      yyc: res.yyc || 0,
    };
    clList.value = res.rows.filter((item) => item.type == 'cl');
    clList.value.tag = 'cl';
    bjList.value = res.rows.filter((item) => item.type == 'bj');
    bjList.value.tag = 'bj';
    rlList.value = res.rows.filter((item) => item.type == 'rl');
    rlList.value.tag = 'rl';
    qtList.value = res.rows.filter((item) => item.type != 'cl' && item.type != 'bj' && item.type != 'rl');
    qtList.value.tag = 'qt';
    // 隐藏
    dlList.value = res.rows.filter((item) => item.type == 's' || item.type == 'd');
    dlList.value.tag = 'dl';
    zzfyList.value = res.rows.filter((item) => item.type == 'zzfy');
    zzfyList.value.tag = 'zzfy';

    // 根据当前日期判断可编辑状态
    const currentDay = date.date();
    // 上半月(1-15号)：允许编辑月预算，不允许编辑月预测
    canEditYys.value = currentDay <= 15;
    canEditYyc.value = currentDay > 15;

    isSave.value = true;
    firstWatch = true;
    show.value = true;
  };
  const presets = ref([
    { label: '昨天', value: dayjs().add(-1, 'd') },
    { label: '明天', value: dayjs().add(1, 'd') },
    { label: '上周', value: dayjs().add(-7, 'd') },
    { label: '上个月', value: dayjs().add(-1, 'month') },
  ]);
</script>
<style lang="less" scoped>
  @import url(@/views/cw/public/style/table.less);
  .grid-table > div {
    grid-template-columns: repeat(11, 1fr); /* 11 列 */
  }
</style>
