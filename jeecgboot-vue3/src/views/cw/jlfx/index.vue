<template>
  <div class="table-container">
    <div class="toolbar">
      <div class="toolbar-actions">
        <a-button type="primary" @click="submit">提交</a-button>
      </div>
      <div class="toolbar-filters">
        <span>日期范围:</span>
        <a-date-picker picker="month" v-model:value="dateRange" @change="dateRangeChange" :allowClear="false" />
      </div>
    </div>
    <div class="flex justify-between items-center">
      <div></div>
      <div class="title items-center">
        <div>价量分析表</div>
        <div v-show="!isSave" class="text-red">*</div>
      </div>
      <div class="operator">
        <!-- <a-tooltip>
          <template #title>自动填充</template>
          <SisternodeOutlined class="mr-6 text-lg" @click="clickAutoFill" />
        </a-tooltip> -->
        <!--        <a-tooltip>-->
        <!--          <template #title>自动保存</template>-->
        <!--          <CloudSyncOutlined class="mr-6 text-lg" @click="clickAutoSave" :class="autoSave ? 'text-blue' : ''" />-->
        <!--        </a-tooltip>-->
        <div class="mr-10"></div>
      </div>
    </div>
    <div class="grid-table">
      <!-- 表头 -->
      <div class="row header">
        <div></div>
        <div>实际</div>
        <div>计划</div>
        <div>同比增减幅度</div>
        <div>影响利润金额(万元)</div>
      </div>
      <!-- 数据行 -->
      <div class="row">
        <!--  项目  -->
        <div>产品价量对利润影响小计</div>
        <!--  实际  -->
        <div></div>
        <!--  计划  -->
        <div></div>
        <!--  同比增减幅度     -->
        <div></div>
        <!--   影响利润金额     -->
        <div>{{ formatNumber(jeSum) }}</div>
      </div>
      <div class="row">
        <!--  项目  -->
        <div>产品售价</div>
        <!--  实际,计划，同比增减幅度 -->
        <div class="span-3">合计</div>
        <div>{{ formatNumber(sjSum) }}</div>
      </div>
      <div class="row" v-for="(d, i) in sjData" :key="d.name">
        <!--  项目  -->
        <div>{{ d.name }}</div>
        <!--  实际  -->
        <div>{{ formatNumber(d.sj) }}</div>
        <!--  计划  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="d.jh" /></div>
        <!--  同比增减幅度     -->
        <div>{{ formatNumber(((d.sj - d.jh) / d.jh) * 100) }}%</div>
        <!--   影响利润金额     -->
        <div>{{ formatNumber(sjJeList[i]) }}</div>
      </div>
      <div class="row">
        <!--  项目  -->
        <div>产品销量</div>
        <!--  实际,计划，同比增减幅度 -->
        <div class="span-3">合计</div>
        <!--   影响利润金额     -->
        <div>{{ formatNumber(xlSum) }}</div>
      </div>
      <div class="row" v-for="(d, i) in xlData" :key="d.name">
        <!--  项目  -->
        <div>{{ d.name }}</div>
        <!--  实际  -->
        <div>{{ formatNumber(d.sj) }}</div>
        <!--  计划  -->
        <div><a-input-number class="custom-center-text" size="small" :bordered="false" v-model:value="d.jh" /></div>
        <!--  同比增减幅度     -->
        <div>{{ formatNumber(((d.sj - d.jh) / d.jh) * 100) }}%</div>
        <!--   影响利润金额     -->
        <div>{{ formatNumber(xlJeList[i]) }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup name="cw-jlfx">
  import { computed, onMounted, ref, watch, toRaw } from 'vue';
  // 项目字典
  import { defHttp } from '@/utils/http/axios';
  import dayjs, { Dayjs } from 'dayjs';
  import { formatNumber } from '@/utils/showUtils';
  import { message } from 'ant-design-vue';

  type jlfx = { name: string; sj: number; jh: number; type: string; nameKey: string };

  // 产品配置（支持 nameKey 和中文名称双键）
  const ITEM_CONFIG: Record<string, { coefficient: number; withoutTax: boolean }> = {
    // 铜
    t: { coefficient: 0.9, withoutTax: true },
    含铜: { coefficient: 0.9, withoutTax: true },
    铜: { coefficient: 0.9, withoutTax: true },
    // 金
    j: { coefficient: 0.84, withoutTax: false },
    含金: { coefficient: 0.84, withoutTax: false },
    金: { coefficient: 0.84, withoutTax: false },
    // 银
    y: { coefficient: 0.75, withoutTax: true },
    含银: { coefficient: 0.75, withoutTax: true },
    银: { coefficient: 0.75, withoutTax: true },
    // 硫精矿
    ljk: { coefficient: 1, withoutTax: true },
    硫精矿: { coefficient: 1, withoutTax: true },
    // 钼产品
    m: { coefficient: 1, withoutTax: true },
    钼产品: { coefficient: 1, withoutTax: true },
  };

  // 获取数据
  const sjData = ref<Array<jlfx>>([]);
  const xlData = ref<Array<jlfx>>([]);
  let oldData: String = '';
  let isSave = ref(true);
  const show = ref(false);
  // 日期选择
  const dateRange = ref(dayjs(new Date()));
  onMounted(async () => {
    await httpGetData(dateRange.value);
  });
  // 是否保存
  watch(
    () => [sjData.value, xlData.value],
    (newValue) => {
      isSave.value = oldData === JSON.stringify(toRaw(newValue));
    },
    { deep: true }
  );
  // 日期选择器变化
  const dateRangeChange = async (v) => {
    await httpGetData(dayjs(v));
  };
  // 判断值是否为有效数字
  const isNum = (v: unknown): v is number => typeof v === 'number' && !isNaN(v);
  // 影响利润金额
  const sjJeList = computed<Array<number>>(() => {
    const result: Array<number> = [];
    for (let i = 0; i < sjData.value.length; i++) {
      const priceRow = sjData.value[i];
      const volumeRow = xlData.value[i]; // 对应销量行

      // 若数据缺失则记 0
      if (!isNum(priceRow?.sj) || !isNum(priceRow?.jh) || !isNum(volumeRow?.sj)) {
        result.push(0);
        continue;
      }

      const cfgKey = priceRow.nameKey ?? priceRow.name;
      const cfg = ITEM_CONFIG[cfgKey] || { coefficient: 1, withoutTax: true };

      // 价格差异（根据是否去税）
      const priceDiff = cfg.withoutTax ? priceRow.sj / 1.13 - priceRow.jh / 1.13 : priceRow.sj - priceRow.jh;
      const impact = ((priceDiff * volumeRow.sj) / 10000) * cfg.coefficient;
      result.push(impact);
    }
    return result;
  });
  const xlJeList = computed<Array<number>>(() => {
    const result: Array<number> = [];
    for (let i = 0; i < xlData.value.length; i++) {
      const volumeRow = xlData.value[i];
      const priceRow = sjData.value[i]; // 对应价格行，取计划价

      // 数据缺失 -> 0
      if (!isNum(volumeRow?.sj) || !isNum(volumeRow?.jh) || !isNum(priceRow?.jh)) {
        result.push(0);
        continue;
      }

      const cfgKey2 = volumeRow.nameKey ?? volumeRow.name;
      const cfg = ITEM_CONFIG[cfgKey2] || { coefficient: 1, withoutTax: true };

      // 计划单价（根据是否去税）
      const planPrice = cfg.withoutTax ? priceRow.jh / 1.13 : priceRow.jh;

      // 销量差异
      const volumeDiff = volumeRow.sj - volumeRow.jh;
      const impact = ((volumeDiff * planPrice) / 10000) * cfg.coefficient;
      result.push(impact);
    }
    return result;
  });
  const sjSum = computed<number>(() => {
    return sjJeList.value.reduce((acc, cur) => acc + cur, 0);
  });
  const xlSum = computed<number>(() => {
    return xlJeList.value.reduce((acc, cur) => acc + cur, 0);
  });
  const jeSum = computed<number>(() => {
    return sjSum.value + xlSum.value;
  });

  // 提交
  const submit = async () => {
    // 提交数据
    await defHttp.post({
      url: 'jlfx/cwJlfxMonth/submit',
      params: {
        submitDate: dayjs(dateRange.value).format('YYYY-MM-DD'),
        rows: [...sjData.value, ...xlData.value],
      },
    });
    httpGetData(dateRange.value);
  };

  // 自动填充
  const clickAutoFill = async () => {
    const res = await defHttp.get({
      url: 'jlfx/cwJlfxMonth/autoFill',
      params: {
        queryDate: dayjs(dateRange.value).format('YYYY-MM-DD'),
      },
    });
    if (res == null) {
      message.warn('无数据填充');
      return;
    }
    packResult(res);
    message.info('填充完成');
  };

  // =====================================公共函数
  const httpGetData = async (date: Dayjs) => {
    show.value = false;
    let queryDate = dayjs(date).format('YYYY-MM-DD');
    let res = await defHttp.get({ url: '/jlfx/cwJlfxMonth/query', params: { queryDate } });
    sjData.value = res.rows.filter((d: jlfx) => d.type === 'sj');
    xlData.value = res.rows.filter((d: jlfx) => d.type === 'xl');
    oldData = JSON.stringify([sjData.value, xlData.value]);
    isSave.value = true;
    show.value = true;
  };

  const packResult = (res) => {
    sjData.value = res.rows.filter((d: jlfx) => d.type === 'sj');
    xlData.value = res.rows.filter((d: jlfx) => d.type === 'xl');
  };
</script>
<style lang="less" scoped>
  @import url(@/views/cw/public/style/ant-input.css);

  :deep(.custom-center-text) {
    width: 50%;
  }

  /* 小计单元格样式 */
  .span-3 {
    grid-column: span 3; /* 跨越3列 */
    background-color: #e6ffed;
    color: #28a745;
    font-weight: bold;
  }

  /* 容器样式 */
  .table-container {
    width: 100%;
    //max-width: 1500px;
    margin: 0 auto;
    background: linear-gradient(180deg, #f5f7fa 0%, #eef1f5 100%);
    padding: 20px;
    border-radius: 12px;
    box-shadow:
      0 4px 6px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08);
  }

  /* 工具栏样式 */
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #eaeaea;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }

  /* 工具栏动作区域 */
  .toolbar-actions {
    display: flex;
    gap: 10px;
  }

  /* 提交按钮样式 */
  .submit-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .submit-button:hover {
    background-color: #0056b3;
  }

  /* 工具栏筛选区域 */
  .toolbar-filters {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .toolbar-filters span {
    font-weight: bold;
  }

  /* 时间选择器样式 */
  .ant-calendar-picker-input.ant-input {
    height: 36px;
    padding: 0 10px;
    font-size: 14px;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-image: none;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    transition: all 0.3s;
  }

  .ant-calendar-picker-input.ant-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
  }

  /* 容器样式 */
  .table-container {
    width: 100%;
    //max-width: 1800px;
    margin: 0 auto;
    background: linear-gradient(180deg, #f5f7fa 0%, #eef1f5 100%);
    padding: 20px;
    border-radius: 12px;
    box-shadow:
      0 4px 6px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08);
  }

  /* 标题样式 */
  .title {
    display: flex;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    text-align: center;
    padding: 15px 0;
  }

  /* 表格基础样式 */
  .grid-table {
    display: grid;
    gap: 5px;
    border-radius: 6px;
    overflow: hidden;
    background-color: #ffffff; /* 确保表格内部有清晰对比 */
  }

  /* 行样式 */
  .row {
    display: contents; /* 让子元素直接成为网格项 */
  }

  .custom-center-text :deep(.ant-input-number-input) {
    text-align: center !important;
  }

  :deep(.custom-center-text) {
    text-align: center !important;
  }

  /* 表头样式 */
  .header {
    font-weight: bold;
    color: white;
    background: linear-gradient(135deg, #007bff, #1e90ff);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  /* 定义每一列的宽度 */
  .grid-table > div {
    display: grid;
    grid-template-columns: repeat(5, 1fr); /* 5 列 */
    padding: 10px;
    text-align: center;
    border-right: 1px solid #eaeaea;
    transition: background-color 0.2s;

    &:last-child {
      border-right: none;
    }
  }

  /* 单元格悬停效果 */
  .grid-table > div:hover {
    background-color: #e9ecef;
  }

  /* 小计单元格样式 */
  .subtotal .span-3 {
    grid-column: span 3; /* 跨越3列 */
    background-color: #e6ffed;
    color: #28a745;
    font-weight: bold;
  }

  /* 小计行项目名称样式 */
  .subtotal > div:first-child {
    font-weight: bold;
  }

  /* 行交替背景色 */
  .grid-table > :nth-child(even) {
    background-color: #f8f9fa;
  }

  .grid-table > :nth-child(odd) {
    background-color: #ffffff;
  }
</style>
